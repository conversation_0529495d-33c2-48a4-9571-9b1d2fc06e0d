"use client";

import * as React from "react";
import { Copy, Check } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";

interface CopyButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
  className?: string;
  iconSize?: string;
  timeout?: number;
}

function CopyButton({
  text,
  className,
  iconSize = "size-5",
  timeout = 2000,
  ...props
}: CopyButtonProps) {
  const [isCopied, setIsCopied] = React.useState(false);
  const [isDisabled, setIsDisabled] = React.useState(false);

  const handleCopy = async () => {
    if (isDisabled) return;

    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);
      setIsDisabled(true);

      setTimeout(() => {
        setIsCopied(false);
        setIsDisabled(false);
      }, timeout);
    } catch (error) {
      console.error("Failed to copy text:", error);
      // Reset states even if copy failed
      setIsDisabled(false);
    }
  };

  return (
    <button
      data-slot="copy-button"
      onClick={handleCopy}
      disabled={isDisabled}
      className={cn(
        "inline-flex items-center justify-center disabled:pointer-events-none",
        className,
      )}
      {...props}
    >
      {isCopied ? (
        <Check className={iconSize} />
      ) : (
        <Copy className={iconSize} />
      )}
    </button>
  );
}

export { CopyButton };
