import { useQuery } from "@tanstack/react-query";
import { NestedCheckboxItem } from "@workspace/ui/components/nested-checkbox";

// Mock function to simulate API call for activity categories
const fetchActivityCategories = async (): Promise<NestedCheckboxItem[]> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 50));
  
  // Return the categories data structure
  return [
    {
      id: "oyun",
      label: "<PERSON>yun",
      children: [
        {
          id: "rekabet<PERSON>",
          label: "<PERSON><PERSON><PERSON>çi",
          children: [
            { id: "fps", label: "FPS" },
            { id: "moba", label: "MOBA" },
            { id: "battle-royale", label: "Battle Royale" },
          ],
        },
        {
          id: "senaryo",
          label: "<PERSON><PERSON><PERSON> Odaklı",
        },
        {
          id: "mmo-rpg",
          label: "MMO",
        },
        {
          id: "masa-parti",
          label: "<PERSON><PERSON> Oyunları",
        },
      ],
    },
    {
      id: "reaksiyon",
      label: "<PERSON>aks<PERSON><PERSON>",
    },
    {
      id: "uretim",
      label: "Üretim",
    },
  ];
};

export const useActivityCategories = () => {
  return useQuery({
    queryKey: ["activity-categories"],
    queryFn: fetchActivityCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes - categories don't change often
  });
};
