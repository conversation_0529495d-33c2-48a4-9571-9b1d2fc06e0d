import * as React from "react";

interface PlaceholderUserProps extends React.SVGProps<SVGSVGElement> {
  size?: number | string;
}

const PlaceholderUser = React.forwardRef<SVGSVGElement, PlaceholderUserProps>(
  ({ size = 24, className, ...props }, ref) => (
    <svg
      ref={ref}
      width={size}
      height={size}
      viewBox="0 0 1024 1024"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path d="m 363.5625,498.03516 c -3.74359,-0.0769 -7.60888,1.22239 -11.57813,3.14453 -30.25707,14.65216 -58.64514,32.31427 -84.56835,53.69726 -31.16382,25.70569 -57.94135,55.5077 -80.03907,89.35352 -29.2279,44.76654 -48.11957,93.68652 -58.01367,146.17969 -4.66681,24.75994 -7.84259,49.61108 -7.27734,74.88867 0.40295,18.01696 6.74939,33.64702 18.81445,46.63476 22.51417,24.23584 50.13842,39.87836 82.63281,46.47461 5.98327,1.21454 12.23682,1.51001 18.36719,1.52539 47.32605,0.11835 94.65216,0.0637 142.47852,0.0625 40.81595,0.004 81.58086,-0.0126 122.31054,-0.0234 40.72969,0.0108 81.49459,0.0271 122.31055,0.0234 47.82636,0.001 95.15247,0.0558 142.47852,-0.0625 6.13037,-0.0154 12.38391,-0.31085 18.36718,-1.52539 32.49439,-6.59625 60.11865,-22.23877 82.63282,-46.47461 12.06506,-12.98774 18.4115,-28.6178 18.81445,-46.63476 0.56525,-25.27759 -2.61053,-50.12873 -7.27735,-74.88867 -9.8941,-52.49317 -28.78577,-101.41315 -58.01367,-146.17969 -22.09772,-33.84582 -48.87524,-63.64783 -80.03906,-89.35352 -25.92321,-21.38299 -54.31128,-39.0451 -84.56836,-53.69726 -7.93848,-3.84427 -15.46093,-5.19553 -22.42773,1.96484 -1.36139,1.3992 -3.30323,2.22281 -4.94532,3.35938 -39.33142,27.22355 -81.83124,40.50383 -127.33203,40.1875 -45.50079,0.31633 -88.0006,-12.96395 -127.33203,-40.1875 -1.64209,-1.13657 -3.58392,-1.96018 -4.94531,-3.35938 -3.4834,-3.58018 -7.10602,-5.03252 -10.84961,-5.10937 z" />
      <path d="m 506.74805,65.576172 c -10.35377,0.125944 -20.91453,0.961444 -31.68164,2.513672 -58.00479,8.36219 -105.26899,36.521426 -141.08399,82.851566 -40.95407,52.97804 -53.84943,112.84588 -39.86133,178.10937 10.40146,48.52939 35.50449,88.71543 73.38672,120.68555 31.24692,26.37031 67.01584,42.94092 107.79492,48.54101 10.54352,1.44794 21.02566,2.20931 31.44532,2.25196 10.41966,-0.0426 20.90375,-0.80402 31.44726,-2.25196 40.77908,-5.60009 76.548,-22.1707 107.79492,-48.54101 37.88223,-31.97012 62.98526,-72.15616 73.38672,-120.68555 13.9881,-65.26349 1.09274,-125.13133 -39.86133,-178.10937 C 643.70063,104.61127 596.43643,76.452034 538.43164,68.089844 527.66453,66.537616 517.10181,65.702116 506.74805,65.576172 Z" />
    </svg>
  ),
);

PlaceholderUser.displayName = "PlaceholderUser";

export { PlaceholderUser };
