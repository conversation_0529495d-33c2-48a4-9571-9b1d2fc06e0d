"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Check, Clock, Circle, X } from "lucide-react";
import { useForm } from "@tanstack/react-form";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import placeholderUserSvg from "@/svg/placeholder-user.svg";

export interface KYCDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: KYCFormData) => void;
  onCancel?: () => void;
  status?: KYCStatus; // Provided by server/admin
}

export interface KYCFormData {
  firstName: string;
  lastName: string;
}

export function KYCDialog({
  open = true,
  onOpenChange,
  onSubmit,
  onCancel,
  status = "not_submitted",
}: KYCDialogProps) {
  const form = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
    },
    onSubmit: async ({ value }) => {
      onSubmit?.(value);
    },
    validators: {
      onChange: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.firstName.trim()) {
          errors.firstName = "Ad gereklidir";
        } else if (value.firstName.trim().length < 2) {
          errors.firstName = "Ad en az 2 karakter olmalıdır";
        }

        if (!value.lastName.trim()) {
          errors.lastName = "Soyad gereklidir";
        } else if (value.lastName.trim().length < 2) {
          errors.lastName = "Soyad en az 2 karakter olmalıdır";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{"Kimlik Doğrulama (KYC)"}</DialogTitle>
            <DialogDescription>
              {
                "Bu bilgiler finansal işlemlerinizde kimlik doğrulama ve güvenlik protokollerimiz kapsamında kullanılacaktır. Lütfen bilgilerinizi eksiksiz ve doğru bir şekilde giriniz."
              }
            </DialogDescription>
          </DialogHeader>

          {/* ID Card Container */}
          <div className="mx-auto p-6 max-w-[400px] rounded-xl bg-muted shadow-lg border-2 border-muted-foreground">
            {/* ID Card Header with Chip */}

            {/* ID Card Content with Photo and Fields */}
            <div className="flex gap-6">
              {/* Photo Rectangle */}
              <div className="w-31 h-36 grid-background-avatar rounded-md flex items-center justify-center flex-shrink-0 relative overflow-hidden border-4 border-input bg-right"></div>

              {/* Form Fields - Vertical Layout */}
              <div className="flex-1 space-y-4">
                <form.Field
                  name="firstName"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value.trim()) return "Ad gereklidir";
                      if (value.trim().length < 2)
                        return "Ad en az 2 karakter olmalıdır";
                      return undefined;
                    },
                  }}
                >
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Ad"}</Label>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                      />
                      {field.state.meta.errors.length > 0 && (
                        <span className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </span>
                      )}
                    </div>
                  )}
                </form.Field>

                <form.Field
                  name="lastName"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value.trim()) return "Soyad gereklidir";
                      if (value.trim().length < 2)
                        return "Soyad en az 2 karakter olmalıdır";
                      return undefined;
                    },
                  }}
                >
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Soyad"}</Label>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                      />
                      {field.state.meta.errors.length > 0 && (
                        <span className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </span>
                      )}
                    </div>
                  )}
                </form.Field>
              </div>
            </div>

            {/* KYC Status with Chip */}
            <div className="mt-6 flex items-center justify-between gap-3">
              {/* Status Pill */}
              <KYCStatusBadge status={status} />

              {/* Mini Chip Shape */}
              <div className="w-10 h-8 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm border border-yellow-700 shadow-sm relative saturate-25">
                <div className="absolute inset-[3px] ">
                  <div className="grid grid-cols-3 gap-px h-full p-0.5">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="bg-yellow-600 rounded-[0.5px]" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
            >
              {([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  variant="primary"
                  disabled={!canSubmit || isSubmitting}
                >
                  {isSubmitting ? "GÖNDERILIYOR..." : "GÖNDER"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
export type KYCStatus = "not_submitted" | "submitted" | "rejected" | "pending";

export interface KYCStatusBadgeProps {
  status: KYCStatus;
  className?: string;
}

export function KYCStatusBadge({ status, className }: KYCStatusBadgeProps) {
  switch (status) {
    case "submitted":
      return (
        <Badge className={className}>
          <Check /> KYC Gönderildi
        </Badge>
      );
    case "rejected":
      return (
        <Badge variant="destructive" className={className}>
          <X /> KYC Reddedildi
        </Badge>
      );
    case "pending":
      return (
        <Badge variant="secondary" className={className}>
          <Clock />
        </Badge>
      );
    case "not_submitted":
    default:
      return (
        <Badge variant="outline" className={className}>
          <Circle />
          {"Henüz Gönderilmedi"}
        </Badge>
      );
  }
}
