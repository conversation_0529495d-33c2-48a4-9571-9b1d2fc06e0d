"use client";

import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon, MinusIcon } from "lucide-react";

import { cn } from "@workspace/ui/lib/utils";
import {
  checkboxVariants,
  checkboxIndicatorVariants,
  checkboxIconVariants,
} from "@workspace/ui/components/checkbox";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@workspace/ui/components/accordion";
import { Label } from "@workspace/ui/components/label";

// Types for the nested checkbox data structure
export interface NestedCheckboxItem {
  id: string;
  label: string;
  children?: NestedCheckboxItem[];
}

export interface NestedCheckboxState {
  [id: string]: boolean | "indeterminate";
}

// Custom checkbox component that supports indeterminate state
function CheckboxWithIndeterminate({
  className,
  checked,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root> & {
  checked?: boolean | "indeterminate";
}) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        checkboxVariants({ size: "large" }),
        // Add indeterminate state styling
        checked === "indeterminate" &&
          "bg-primary text-primary-foreground border-primary",
        className,
      )}
      checked={checked === "indeterminate" ? "indeterminate" : checked}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className={checkboxIndicatorVariants()}
      >
        {checked === "indeterminate" ? (
          <MinusIcon className={checkboxIconVariants({ size: "large" })} />
        ) : (
          <CheckIcon className={checkboxIconVariants({ size: "large" })} />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

// Hook to manage nested checkbox state
function useNestedCheckboxState(
  items: NestedCheckboxItem[],
  initialState?: NestedCheckboxState,
) {
  const [state, setState] = React.useState<NestedCheckboxState>(
    initialState || {},
  );

  // Get all item IDs recursively
  const getAllItemIds = React.useCallback(
    (items: NestedCheckboxItem[]): string[] => {
      const ids: string[] = [];
      const traverse = (items: NestedCheckboxItem[]) => {
        items.forEach((item) => {
          ids.push(item.id);
          if (item.children) {
            traverse(item.children);
          }
        });
      };
      traverse(items);
      return ids;
    },
    [],
  );

  // Get children IDs for a specific item
  const getChildrenIds = React.useCallback(
    (item: NestedCheckboxItem): string[] => {
      if (!item.children) return [];
      return getAllItemIds(item.children);
    },
    [getAllItemIds],
  );

  // Calculate the state of a parent based on its children
  const calculateParentState = React.useCallback(
    (
      item: NestedCheckboxItem,
      currentState: NestedCheckboxState,
    ): boolean | "indeterminate" => {
      if (!item.children || item.children.length === 0) {
        return currentState[item.id] || false;
      }

      const childrenStates = item.children.map((child) => {
        if (child.children && child.children.length > 0) {
          return calculateParentState(child, currentState);
        }
        return currentState[child.id] || false;
      });

      const checkedCount = childrenStates.filter(
        (state) => state === true,
      ).length;
      const indeterminateCount = childrenStates.filter(
        (state) => state === "indeterminate",
      ).length;

      if (checkedCount === childrenStates.length) {
        return true;
      } else if (checkedCount === 0 && indeterminateCount === 0) {
        return false;
      } else {
        return "indeterminate";
      }
    },
    [],
  );

  // Update state and propagate changes
  const updateState = React.useCallback(
    (itemId: string, checked: boolean) => {
      setState((prevState) => {
        const newState = { ...prevState };

        // Find the item being changed
        const findItem = (
          items: NestedCheckboxItem[],
        ): NestedCheckboxItem | null => {
          for (const item of items) {
            if (item.id === itemId) return item;
            if (item.children) {
              const found = findItem(item.children);
              if (found) return found;
            }
          }
          return null;
        };

        const item = findItem(items);
        if (!item) return newState;

        // Update the clicked item
        newState[itemId] = checked;

        // Update all children
        const childrenIds = getChildrenIds(item);
        childrenIds.forEach((childId) => {
          newState[childId] = checked;
        });

        // Recalculate all parent states to ensure consistency
        const updateParentStates = (items: NestedCheckboxItem[]) => {
          items.forEach((item) => {
            if (item.children && item.children.length > 0) {
              updateParentStates(item.children);
              newState[item.id] = calculateParentState(item, newState);
            }
          });
        };
        updateParentStates(items);

        return newState;
      });
    },
    [items, getChildrenIds, calculateParentState],
  );

  return {
    state,
    updateState,
    setState,
  };
}

// Individual checkbox item component
interface NestedCheckboxItemComponentProps {
  item: NestedCheckboxItem;
  state: NestedCheckboxState;
  onStateChange: (itemId: string, checked: boolean) => void;
  level?: number;
  expandedItems: string[];
}

function NestedCheckboxItemComponent({
  item,
  state,
  onStateChange,
  level = 0,
  expandedItems,
}: NestedCheckboxItemComponentProps) {
  const hasChildren = item.children && item.children.length > 0;
  const isChecked = state[item.id] || false;

  const handleCheckboxChange = (checked: boolean) => {
    onStateChange(item.id, checked);
  };

  const handleLabelClick = () => {
    handleCheckboxChange(!isChecked);
  };

  if (hasChildren) {
    return (
      <AccordionItem value={item.id} className="border-none">
        <div className="flex items-center gap-3 mb-3">
          <AccordionTrigger />
          <CheckboxWithIndeterminate
            checked={isChecked}
            onCheckedChange={handleCheckboxChange}
          />
          <Label className="cursor-pointer" onClick={handleLabelClick}>
            {item.label}
          </Label>
        </div>
        <AccordionContent className="pb-2">
          <div className="ml-4 space-y-2">
            {item.children?.map((child) => (
              <NestedCheckboxItemComponent
                key={child.id}
                item={child}
                state={state}
                onStateChange={onStateChange}
                level={level + 1}
                expandedItems={expandedItems}
              />
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    );
  }

  return (
    <div className="ml-8 flex items-center gap-3 mb-3">
      <CheckboxWithIndeterminate
        checked={isChecked}
        onCheckedChange={handleCheckboxChange}
      />
      <Label className="cursor-pointer" onClick={handleLabelClick}>
        {item.label}
      </Label>
    </div>
  );
}

// Main NestedCheckbox component
export interface NestedCheckboxProps {
  items: NestedCheckboxItem[];
  value?: NestedCheckboxState;
  onValueChange?: (state: NestedCheckboxState) => void;
  className?: string;
  defaultValue?: NestedCheckboxState;
}

function NestedCheckbox({
  items,
  value,
  onValueChange,
  className,
  defaultValue,
}: NestedCheckboxProps) {
  const { state, updateState, setState } = useNestedCheckboxState(
    items,
    defaultValue,
  );

  // Use controlled state if provided
  const currentState = value !== undefined ? value : state;

  // State for managing accordion expansion
  const [userExpandedItems, setUserExpandedItems] = React.useState<string[]>(
    [],
  );

  // Calculate which items should be auto-expanded (items with indeterminate state)
  const getAutoExpandedItems = React.useCallback(
    (items: NestedCheckboxItem[], state: NestedCheckboxState): string[] => {
      const expandedItems: string[] = [];

      const checkItem = (item: NestedCheckboxItem) => {
        if (item.children && item.children.length > 0) {
          const itemState = state[item.id];
          if (itemState === "indeterminate") {
            expandedItems.push(item.id);
          }
          // Recursively check children
          item.children.forEach(checkItem);
        }
      };

      items.forEach(checkItem);
      return expandedItems;
    },
    [],
  );

  const autoExpandedItems = getAutoExpandedItems(items, currentState);

  // Combine user-expanded items with auto-expanded items
  const allExpandedItems = React.useMemo(() => {
    const combined = new Set([...userExpandedItems, ...autoExpandedItems]);
    return Array.from(combined);
  }, [userExpandedItems, autoExpandedItems]);

  const handleStateChange = React.useCallback(
    (itemId: string, checked: boolean) => {
      if (value !== undefined) {
        // Controlled mode - calculate new state and pass to parent
        const newState = { ...currentState };

        // Find the item being changed
        const findItem = (
          items: NestedCheckboxItem[],
        ): NestedCheckboxItem | null => {
          for (const item of items) {
            if (item.id === itemId) return item;
            if (item.children) {
              const found = findItem(item.children);
              if (found) return found;
            }
          }
          return null;
        };

        const item = findItem(items);
        if (!item) return;

        // Update the clicked item
        newState[itemId] = checked;

        // Update all children
        const getAllItemIds = (items: NestedCheckboxItem[]): string[] => {
          const ids: string[] = [];
          const traverse = (items: NestedCheckboxItem[]) => {
            items.forEach((item) => {
              ids.push(item.id);
              if (item.children) {
                traverse(item.children);
              }
            });
          };
          traverse(items);
          return ids;
        };

        const getChildrenIds = (item: NestedCheckboxItem): string[] => {
          if (!item.children) return [];
          return getAllItemIds(item.children);
        };

        const childrenIds = getChildrenIds(item);
        childrenIds.forEach((childId: string) => {
          newState[childId] = checked;
        });

        // Recalculate all parent states
        const calculateParentState = (
          item: NestedCheckboxItem,
          currentState: NestedCheckboxState,
        ): boolean | "indeterminate" => {
          if (!item.children || item.children.length === 0) {
            return currentState[item.id] || false;
          }

          const childrenStates = item.children.map((child) => {
            if (child.children && child.children.length > 0) {
              return calculateParentState(child, currentState);
            }
            return currentState[child.id] || false;
          });

          const checkedCount = childrenStates.filter(
            (state) => state === true,
          ).length;
          const indeterminateCount = childrenStates.filter(
            (state) => state === "indeterminate",
          ).length;

          if (checkedCount === childrenStates.length) {
            return true;
          } else if (checkedCount === 0 && indeterminateCount === 0) {
            return false;
          } else {
            return "indeterminate";
          }
        };

        const updateParentStates = (items: NestedCheckboxItem[]) => {
          items.forEach((item) => {
            if (item.children && item.children.length > 0) {
              updateParentStates(item.children);
              newState[item.id] = calculateParentState(item, newState);
            }
          });
        };
        updateParentStates(items);

        onValueChange?.(newState);
      } else {
        // Uncontrolled mode - manage state internally
        updateState(itemId, checked);
      }
    },
    [value, currentState, onValueChange, updateState, items],
  );

  // Sync external state changes in controlled mode
  React.useEffect(() => {
    if (value !== undefined) {
      setState(value);
    }
  }, [value, setState]);

  return (
    <div className={cn("space-y-1", className)}>
      <Accordion
        type="multiple"
        value={allExpandedItems}
        onValueChange={setUserExpandedItems}
        className="w-full"
      >
        {items.map((item) => (
          <NestedCheckboxItemComponent
            key={item.id}
            item={item}
            state={currentState}
            onStateChange={handleStateChange}
            expandedItems={allExpandedItems}
          />
        ))}
      </Accordion>
    </div>
  );
}

export { NestedCheckbox, useNestedCheckboxState };
