"use client";

import {
  NestedCheckbox,
  NestedCheckboxItem,
} from "@workspace/ui/components/nested-checkbox";

// 3-layered hierarchical data
const threeLevelData: NestedCheckboxItem[] = [
  {
    id: "development",
    label: "Software Development",
    children: [
      {
        id: "frontend",
        label: "Frontend",
        children: [
          { id: "react", label: "React" },
          { id: "vue", label: "Vue" },
          { id: "angular", label: "Angular" },
        ],
      },
      {
        id: "backend",
        label: "Backend",
        children: [
          { id: "nodejs", label: "Node.js" },
          { id: "python", label: "Python" },
          { id: "java", label: "Java" },
        ],
      },
      {
        id: "mobile",
        label: "Mobile",
        children: [
          { id: "react-native", label: "React Native" },
          { id: "flutter", label: "Flutter" },
          { id: "swift", label: "Swift" },
        ],
      },
    ],
  },
];

export default function NestedCheckboxDemo() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="border-2 rounded-lg p-4">
        <NestedCheckbox items={threeLevelData} />
      </div>
    </div>
  );
}
