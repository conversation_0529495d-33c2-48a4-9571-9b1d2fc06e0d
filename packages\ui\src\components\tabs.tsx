"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@workspace/ui/lib/utils";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("select-none flex flex-col gap-2", className)}
      {...props}
    />
  );
}

function TabsList({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  return (
    <TabsPrimitive.List
      data-slot="tabs-list"
      className={cn(
        "bg-background text-foreground inline-flex w-fit items-center justify-center border-2 rounded-full overflow-clip divide-x-2 divide-border drop-shadow-sm",
        className,
      )}
      {...props}
    />
  );
}

function TabsTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        "group data-[state=active]:bg-primary data-[state=active]: text-foreground data-[state=inactive]:bg-gradient-to-t from-muted-foreground/50 to-background to-10% inline-flex h-[calc(100%-1px)] data-[state=inactive]:inset-shadow-xs data-[state=inactive]:inset-shadow-ring flex-1 items-center justify-center gap-1.5 px-7 py-2 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 data-[state=active]:inset-shadow-sm data-[state=active]:inset-shadow-orange-800/50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 -skew-x-12",
        className,
      )}
      {...props}
    >
      <div className="skew-x-12 fake-text-stroke-muted group-data-[state=active]:translate-y-px">
        {children}
      </div>
    </TabsPrimitive.Trigger>
  );
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
