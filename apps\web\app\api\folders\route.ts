import { NextRequest, NextResponse } from "next/server";
import fs from "node:fs/promises";
import path from "path";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const route = searchParams.get("route") || "";

    // Build the directory path based on the route parameter
    const routeParts = route.split("/").filter(Boolean);
    const targetDir = path.join(process.cwd(), "app", ...routeParts);

    // Check if the directory exists
    let folders: string[] = [];
    try {
      await fs.access(targetDir);
      const entries = await fs.readdir(targetDir, { withFileTypes: true });

      folders = entries
        .filter((entry) => entry.isDirectory())
        .map((entry) => entry.name)
        .filter(
          (name) =>
            !name.startsWith(".") &&
            name !== "node_modules" &&
            name !== "utils" &&
            name !== "api" &&
            name !== "components",
        );
    } catch {
      // If directory doesn't exist, fall back to app root
      const appDir = path.join(process.cwd(), "app");
      const entries = await fs.readdir(appDir, { withFileTypes: true });

      folders = entries
        .filter((entry) => entry.isDirectory())
        .map((entry) => entry.name)
        .filter(
          (name) =>
            !name.startsWith(".") &&
            name !== "node_modules" &&
            name !== "utils" &&
            name !== "api" &&
            name !== "components",
        );
    }

    return NextResponse.json({ folders });
  } catch (error) {
    console.error("Error in folders API:", error);
    return NextResponse.json(
      { error: "Failed to fetch folders" },
      { status: 500 },
    );
  }
}
