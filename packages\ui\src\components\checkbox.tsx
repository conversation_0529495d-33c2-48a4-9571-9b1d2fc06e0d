"use client";

import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

// Shared checkbox variants using cva
const checkboxVariants = cva(
  "peer dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shrink-0 rounded-[4px] transition-shadow disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      size: {
        default: "size-4 border border-input shadow-xs",
        large: "size-5 border-2 border-border",
      },
    },
    defaultVariants: {
      size: "default",
    },
  },
);

// Shared checkbox indicator styles
const checkboxIndicatorVariants = cva(
  "flex items-center justify-center text-current transition-none",
);

// Shared checkbox icon variants
const checkboxIconVariants = cva("", {
  variants: {
    size: {
      default: "size-3.5",
      large: "stroke-3 size-3.5",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

function Checkbox({
  className,
  size,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root> &
  VariantProps<typeof checkboxVariants>) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(checkboxVariants({ size }), className)}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className={checkboxIndicatorVariants()}
      >
        <CheckIcon className={checkboxIconVariants({ size })} />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export {
  Checkbox,
  checkboxVariants,
  checkboxIndicatorVariants,
  checkboxIconVariants,
};
