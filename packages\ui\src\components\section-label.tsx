"use client";

import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";

interface SectionLabelProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

function SectionLabel({ className, children, ...props }: SectionLabelProps) {
  return (
    <div
      data-slot="section-label"
      className={cn("overflow-hidden rounded-xs relative mb-4", className)}
      {...props}
    >
      <span className="inline-block -translate-x-6 bg-foreground -skew-x-24 rounded-xs">
        <span className="inline-block skew-x-24 text-sm text-background text-shadow-none pl-10 pr-5">
          {children}
        </span>
      </span>
    </div>
  );
}

export { SectionLabel };
